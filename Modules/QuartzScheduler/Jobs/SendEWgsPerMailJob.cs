using MediatR;
using Quartz;
using Microsoft.Extensions.Logging;
using ObjectDeAndSerialize;
using QuartzScheduler.Jobs.BaseJobs;
using WingCore.application.Wiegeschein;
using wingPrinterListLabel.application.Wgs;

namespace QuartzScheduler.Jobs;

[DisallowConcurrentExecution]
[PersistJobDataAfterExecution]
public class SendEWgsPerMailJob(ILogger<SendEWgsPerMailJob> logger, ISender mediatr) : BaseJob(logger)
{
    protected override async Task<string> ExecuteJob(IJobExecutionContext context)
    {
        List<LogInfoForEWgs> logInformations = [];

        try
        {
            // Use the BulkWgsSendEmailAutomaticCommand which handles everything
            var successCount = await mediatr.Send(new BulkWgsSendEmailAutomaticCommand(""));

            // Create a summary log entry
            var logInformation = new LogInfoForEWgs
            {
                WgsNumber = 0, // Use 0 to indicate this is a summary entry
                Success = true,
                Email = $"Bulk operation completed successfully. {successCount} emails sent.",
                ErrMsg = null
            };

            logInformations.Add(logInformation);
        }
        catch (Exception e)
        {
            var logInformation = new LogInfoForEWgs
            {
                WgsNumber = 0, // Use 0 to indicate this is a summary entry
                Success = false,
                Email = string.Empty,
                ErrMsg = e.InnerException?.Message ?? e.Message
            };

            logInformations.Add(logInformation);
        }

        return logInformations.SerializeToJson();
    }
}

public record LogInfoForEWgs
{
    public long WgsNumber { get; set; }
    public string Email { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrMsg { get; set; }
}
