using MediatR;
using Quartz;
using Microsoft.Extensions.Logging;
using ObjectDeAndSerialize;
using QuartzScheduler.Jobs.BaseJobs;
using WingCore.application.Wiegeschein;
using wingPrinterListLabel.application.Wgs;

namespace QuartzScheduler.Jobs;

[DisallowConcurrentExecution]
[PersistJobDataAfterExecution]
public class SendEWgsPerMailJob(ILogger<SendEWgsPerMailJob> logger, ISender mediatr) : BaseJob(logger)
{
    protected override async Task<string> ExecuteJob(IJobExecutionContext context)
    {
        List<LogInfoForEWgs> logInformations = [];

        try
        {
            // Use the BulkWgsSendEmailAutomaticCommand which handles everything
            var result = await mediatr.Send(new BulkWgsSendEmailAutomaticCommand(""));

            // Convert each result to the expected log format
            foreach (var wgsResult in result.Results)
            {
                var logInformation = new LogInfoForEWgs
                {
                    WgsNumber = wgsResult.WgsNumber,
                    Success = wgsResult.Success,
                    Email = wgsResult.Email,
                    ErrMsg = wgsResult.ErrorMessage
                };

                logInformations.Add(logInformation);
            }

            // If no individual results, add a summary entry
            if (!result.Results.Any())
            {
                var summaryLog = new LogInfoForEWgs
                {
                    WgsNumber = 0, // Use 0 to indicate this is a summary entry
                    Success = true,
                    Email = $"No WGS records found to process.",
                    ErrMsg = null
                };

                logInformations.Add(summaryLog);
            }
        }
        catch (Exception e)
        {
            var logInformation = new LogInfoForEWgs
            {
                WgsNumber = 0, // Use 0 to indicate this is a summary entry
                Success = false,
                Email = string.Empty,
                ErrMsg = e.InnerException?.Message ?? e.Message
            };

            logInformations.Add(logInformation);
        }

        return logInformations.SerializeToJson();
    }
}

public record LogInfoForEWgs
{
    public long WgsNumber { get; set; }
    public string Email { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrMsg { get; set; }
}
