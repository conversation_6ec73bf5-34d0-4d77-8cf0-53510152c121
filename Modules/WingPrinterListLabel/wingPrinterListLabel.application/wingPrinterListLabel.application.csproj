<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    
    <PropertyGroup Condition="'$(OS)' == 'Windows_NT'">
        <DefineConstants>WINDOWS</DefineConstants>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <NoWarn>1701;1702;CA1416</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <NoWarn>1701;1702;CA1416</NoWarn>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\WingCore\WingCore.application\WingCore.application.csproj" />
      <ProjectReference Include="..\..\WingLager\wingLager.application\wingLager.application.csproj" />
      <ProjectReference Include="..\wingPrinterListLabel.domain\wingPrinterListLabel.domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="MediatR" Version="[12.5.0]" />
    </ItemGroup>

    <ItemGroup Condition="!$([MSBuild]::IsOSPlatform('Windows'))">
        <Reference Include="PrinterService">
            <HintPath>..\..\..\PrinterService\PublishDlls\PrinterService.dll</HintPath>
            <Private>true</Private>
        </Reference>
        <Reference Include="combit.ListLabel30">
            <HintPath>..\..\..\PrinterService\PublishDlls\combit.ListLabel30.dll</HintPath>
            <Private>true</Private>
        </Reference>
    </ItemGroup>

    <ItemGroup Condition="$([MSBuild]::IsOSPlatform('Windows'))">
        <ProjectReference Include="..\..\..\PrinterService\PrinterService.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Invoices\Handler\CommandHandlers\DummyWgsSendEmailCommandHandler.cs" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Wgs\Helpers\" />
    </ItemGroup>

</Project>
