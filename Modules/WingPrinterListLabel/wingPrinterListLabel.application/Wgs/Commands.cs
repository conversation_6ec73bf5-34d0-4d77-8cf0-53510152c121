using MediatR;
using PrinterService.Models;
using WingCore.domain.Common.Configurations;

namespace wingPrinterListLabel.application.Wgs;

// WGS Email Commands
public record DummyWgsSendEmailCommand(string Email,
                                       string LanguageToUse,
                                       ConfigurationElectricWgs ConfigurationElectricWgs) : IRequest<EmailDataToCreateEmail?>;
public record WgsSendEmailCommand(long WgsNumber, string LanguageToUse) : IRequest<EmailDataToCreateEmail?>;
public record BulkWgsSendEmailAutomaticCommand(string LanguageToUse) : IRequest<BulkWgsSendEmailResult>; // For automatic job - returns detailed results

// WGS PDF Commands (printing functionality)
public record WgsAsPdfBase64Command(long WgsNumber, string LanguageToUse) : IRequest<string>;

// Result classes for bulk operations
public record BulkWgsSendEmailResult
{
    public int TotalProcessed { get; init; }
    public int SuccessCount { get; init; }
    public int FailedCount { get; init; }
    public int SkippedCount { get; init; }
    public List<WgsEmailSendResult> Results { get; init; } = [];
}

public record WgsEmailSendResult
{
    public long WgsNumber { get; init; }
    public string Email { get; init; } = string.Empty;
    public bool Success { get; init; }
    public string? ErrorMessage { get; init; }
}
