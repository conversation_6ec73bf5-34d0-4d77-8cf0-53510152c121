using MediatR;
using Microsoft.Extensions.Logging;
using PrinterService;
using PrinterService.Models;
using WingCore.application.Contract;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;
using WingCore.application.Lieferant;
using WingCore.application.PrintableDtos;
using WingCore.application.Wiegeschein;
using WingCore.domain.Common.Configurations;
using WingCore.domain.Common.Flags;
using wingPrinterListLabel.application.Contracts;
using wingPrinterListLabel.application.Wgs.Helpers;

namespace wingPrinterListLabel.application.Wgs.Handler.CommandHandlers;

public class BulkWgsSendEmailAutomaticCommandHandler(ISender mediatr,
                                                     IWgsRepository wgsRepository,
                                                     IApplicationPath applicationPath,
                                                     IConfigurationService configurationService,
                                                     IListLabelRepository lListLabelRepository,
                                                     IUnitOfWork iUnitOfWork,
                                                     ILogger<BulkWgsSendEmailAutomaticCommandHandler> logger) : IRequestHandler<BulkWgsSendEmailAutomaticCommand, BulkWgsSendEmailResult>
{
    public async Task<BulkWgsSendEmailResult> Handle(BulkWgsSendEmailAutomaticCommand command, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Starting automatic WGS email sending job for language: {Language}", command.LanguageToUse);
            
            // Get all WGS that need to be sent automatically (without WgsMailSendSuccess flag)
            var wgsToSend = await mediatr.Send(new WgsToSendEmailAutomaticallyQuery(), cancellationToken);
            var wgsWithLabList = wgsToSend.ToList();

            if (!wgsWithLabList.Any())
            {
                logger.LogInformation("No WGS found that need to be sent automatically");
                return new BulkWgsSendEmailResult
                {
                    TotalProcessed = 0,
                    SuccessCount = 0,
                    FailedCount = 0,
                    SkippedCount = 0,
                    Results = []
                };
            }

            logger.LogInformation("Found {Count} WGS records to send automatically", wgsWithLabList.Count);

            var (eWgsConfig, emailBodyParameter) = await GetEWgsEmailConfig(command.LanguageToUse);
            var successCount = 0;
            var failedCount = 0;
            var skippedCount = 0;
            var results = new List<WgsEmailSendResult>();

            foreach (var wgsWithLab in wgsWithLabList)
            {
                var wgsResult = new WgsEmailSendResult
                {
                    WgsNumber = wgsWithLab.Wgsnr,
                    Success = false
                };

                try
                {
                    // Get email from supplier data using Wgslfnr
                    var eWgsEmail = await GetEmailForWgs(wgsWithLab.Wgs, cancellationToken);

                    if (string.IsNullOrWhiteSpace(eWgsEmail))
                    {
                        logger.LogWarning("No email address found for WGS {WgsNumber} (Supplier: {SupplierId})", wgsWithLab.Wgsnr, wgsWithLab.Wgslfnr);
                        wgsResult = wgsResult with { ErrorMessage = "No email address found for supplier" };
                        results.Add(wgsResult);
                        skippedCount++;
                        continue;
                    }

                    wgsResult = wgsResult with { Email = eWgsEmail };

                    var emailDataToCreateEmail = new EmailDataToCreateEmail
                    {
                        PrintableObject = LabelWiegeschein.Create(wgsWithLab),
                        LayoutFileWithPath = emailBodyParameter.LayoutForPrint,
                        AppFolder = applicationPath.GetPath(),
                        EmailParameter = new EmailParameter()
                        {
                            BodyTemplate = emailBodyParameter.EmailBodyHtml,
                            FileName = emailBodyParameter.FileName,
                            Subject = emailBodyParameter.Subject,
                            
                            SmtpSecureConnection = eWgsConfig.SmtpSecureConnection,
                            SmtpSenderAddress = eWgsConfig.SmtpSenderAddress,
                            SmtpSenderName = eWgsConfig.SmtpSenderName,
                            SmtpServerAddress = eWgsConfig.SmtpServerAddress,
                            SmtpServerPort = eWgsConfig.SmtpServerPort,
                            SmtpServerUser = eWgsConfig.SmtpServerUser,
                            SmtpServerPassword = eWgsConfig.SmtpServerPassword,
                            SmtpSocketTimeout = eWgsConfig.SmtpSocketTimeout,
                            To = eWgsEmail
                        }
                    };

                    // Just attach as PDF, no XML needed for WGS
                    PrinterCreator.SetEmailDataForSendWithPdf(ref emailDataToCreateEmail,
                                                        "",
                                                        "",
                                                        lListLabelRepository);

                    // Actually send the email
                    PrinterCreator.SendEmailWithSetData(emailDataToCreateEmail);

                    // Set WGS flag for email sent success
                    wgsRepository.SetWgsFlag([wgsWithLab.Wgs.Id], WgsFlags.WgsMailSendSuccess);
                    wgsResult = wgsResult with { Success = true };
                    successCount++;

                    logger.LogInformation("Successfully sent WGS email for WGS {WgsNumber} to {Email}", wgsWithLab.Wgsnr, eWgsEmail);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to send WGS email for WGS {WgsNumber}", wgsWithLab.Wgsnr);
                    wgsResult = wgsResult with { ErrorMessage = ex.InnerException?.Message ?? ex.Message };
                    failedCount++;
                }

                results.Add(wgsResult);
            }

            // Save all changes
            await iUnitOfWork.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Automatic WGS email job completed. Success: {SuccessCount}, Failed: {FailedCount}, Skipped: {SkippedCount}",
                                 successCount, failedCount, skippedCount);

            return new BulkWgsSendEmailResult
            {
                TotalProcessed = wgsWithLabList.Count,
                SuccessCount = successCount,
                FailedCount = failedCount,
                SkippedCount = skippedCount,
                Results = results
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during automatic WGS email sending job");
            throw;
        }
    }

    private async Task<string> GetEmailForWgs(WingCore.domain.Models.Wg wgs, CancellationToken cancellationToken)
    {
        // Check if WGS has a supplier number
        if (wgs.Wgslfnr == null || wgs.Wgslfnr == 0)
            return "";

        try
        {
            // Get supplier by number using the existing query handler
            var suppliers = await mediatr.Send(new GetLieferantByNumberQuery(wgs.Wgslfnr.Value), cancellationToken);
            var supplier = suppliers.FirstOrDefault();

            if (supplier == null)
                return "";

            // Check if supplier has a valid email address (not null, not empty, not only spaces)
            if (string.IsNullOrWhiteSpace(supplier.Lfemail))
                return "";

            return supplier.Lfemail.Trim();
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the entire batch
            logger.LogWarning(ex, "Error getting email for WGS {WgsNumber} with supplier {SupplierId}", wgs.Wgsnr, wgs.Wgslfnr);
            return "";
        }
    }

    private async Task<(ConfigurationElectricWgs, LanguageAndEmailFields)> GetEWgsEmailConfig(string languageToUse)
    {
        var eWgsConfig = await configurationService.GetConfigurationElectricWgsAsync();
        if (eWgsConfig is null)
            throw new Exception("Es existiert keine E-Mail konfiguration für E-Wiegescheine");

        LanguageAndEmailFields? emailBodyParameter = null;
        var usedCountry = new List<string>();
        if (!string.IsNullOrWhiteSpace(languageToUse))
        {
            usedCountry.Add(languageToUse);
            eWgsConfig.LanguageToEmailFields.TryGetValue(languageToUse, out emailBodyParameter);
        }

        if (emailBodyParameter is null)
        {
            usedCountry.Add("EN");
            eWgsConfig.LanguageToEmailFields.TryGetValue("EN", out emailBodyParameter);
        }

        if (emailBodyParameter is null)
            throw new Exception($"Es existiert keine E-Mail konfiguration für die Länder '{string.Join(",", usedCountry)}'");

        return (eWgsConfig, emailBodyParameter);
    }
}
